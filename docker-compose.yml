
services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: kds-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: smart_kitchen
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - kds-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend API Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: kds-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      # Database Configuration
      - MONGODB_URL=************************************************************************
      - MONGODB_DATABASE=smart_kitchen

      # Ollama Configuration
      - OLLAMA_BASE_URL=http://************:11434
      - OLLAMA_MODEL=llama3.1:8b

      # API Configuration
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - DEBUG_MODE=false

      # Kitchen Configuration
      - MAX_STARVATION_COUNT=3
      - SYNCHRONIZATION_WINDOW_MINUTES=3
      - PERFORMANCE_HISTORY_DAYS=30

      # Logging
      - LOG_LEVEL=INFO
      - LOG_FILE=logs/kitchen_queue.log

      # Excel Configuration
      - EXCEL_FILE_PATH=data/kitchen_config.xlsx
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      mongodb:
        condition: service_healthy

    networks:
      - kds-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend Web Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=http://localhost:8000
    container_name: kds-frontend
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
      - REACT_APP_API_BASE_URL=http://localhost:8000
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - kds-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

# Networks
networks:
  kds-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  mongodb_data:
    driver: local
