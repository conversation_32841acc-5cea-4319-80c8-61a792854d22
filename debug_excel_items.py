import sys
sys.path.append('.')

from app.services.excel_service import ExcelDataService

def debug_excel_items():
    try:
        excel_service = ExcelDataService()
        print("Excel file path:", excel_service.excel_file_path)
        
        # Load menu items
        items = excel_service.load_menu_items()
        print(f"\nLoaded {len(items)} items from Excel:")
        
        for item_id, item_data in items.items():
            print(f"  - ID: '{item_id}' (type: {type(item_id)}) -> {item_data['name']}")
        
        # Test validation
        test_items = ["1", "2", "3"]
        print(f"\nTesting validation for items: {test_items}")
        
        for item_id in test_items:
            exists = item_id in items
            print(f"  - Item '{item_id}' exists: {exists}")
            if exists:
                available = items[item_id]['available']
                print(f"    Available: {available}")
        
        # Test the validation method
        validation_result = excel_service.validate_order_items(test_items)
        print(f"\nValidation result: {validation_result}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_excel_items()
