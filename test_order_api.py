import requests
import json

# Test the order API
def test_order_creation():
    url = "http://localhost:8000/api/orders/"
    
    # Test data with items from frontend
    test_data = {
        "items": ["1", "2"],  # Classic Spring Rolls and Buffalo Chicken Wings
        "customer_id": "TEST_CUSTOMER"
    }
    
    try:
        print("Testing order creation...")
        print(f"URL: {url}")
        print(f"Data: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(url, json=test_data)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Order creation successful!")
        else:
            print("❌ Order creation failed!")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_order_creation()
