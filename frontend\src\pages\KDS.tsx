
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Grid, List, Clock, Users, Plus, Settings, ShoppingCart, Filter, RefreshCw, MoreVertical, ChevronLeft, AlertCircle } from "lucide-react";
import { Link } from "react-router-dom";

const KDS = () => {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [isManager, setIsManager] = useState(false);
  const [selectedKitchen, setSelectedKitchen] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState("all");
  const [currentTime, setCurrentTime] = useState(new Date());
  
  const [kitchens, setKitchens] = useState([
    { id: "kitchen-1", name: "Main Kitchen", status: "active", orders: 5, staff: 4 },
    { id: "kitchen-2", name: "Grill Station", status: "active", orders: 3, staff: 2 },
    { id: "kitchen-3", name: "Pizza Station", status: "maintenance", orders: 0, staff: 1 },
    { id: "kitchen-4", name: "Dessert Corner", status: "active", orders: 2, staff: 1 }
  ]);
  
  const [newKitchenName, setNewKitchenName] = useState("");
  const [isAddKitchenOpen, setIsAddKitchenOpen] = useState(false);
  
  const [orders, setOrders] = useState([
    {
      id: "ORD-001",
      items: [
        { name: "Burger Deluxe", image: "/lovable-uploads/22d31f51-c174-40a7-bd95-00e4ad00eaf3.png", quantity: 2, calories: 650 },
        { name: "French Fries", image: "/lovable-uploads/5663820f-6c97-4492-9210-9eaa1a8dc415.png", quantity: 1, calories: 350 }
      ],
      status: "pending",
      kitchen: "Main Kitchen",
      priority: "high",
      estimatedTime: "15 min",
      orderTime: "2:30 PM",
      customerName: "John Doe",
      tableNumber: "12"
    },
    {
      id: "ORD-002",
      items: [
        { name: "Pizza Margherita", image: "/lovable-uploads/af412c03-21e4-4856-82ff-d1a975dc84a9.png", quantity: 1, calories: 800 }
      ],
      status: "preparing",
      kitchen: "Pizza Station",
      priority: "medium",
      estimatedTime: "20 min",
      orderTime: "2:25 PM",
      customerName: "Jane Smith",
      tableNumber: "8"
    },
    {
      id: "ORD-003",
      items: [
        { name: "Caesar Salad", image: "/lovable-uploads/c3d5522b-6886-4b75-8ffc-d020016bb9c2.png", quantity: 1, calories: 320 }
      ],
      status: "ready",
      kitchen: "Main Kitchen",
      priority: "low",
      estimatedTime: "5 min",
      orderTime: "2:20 PM",
      customerName: "Mike Johnson",
      tableNumber: "5"
    }
  ]);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  const handleAddKitchen = () => {
    if (newKitchenName.trim()) {
      const newKitchen = {
        id: `kitchen-${Date.now()}`,
        name: newKitchenName.trim(),
        status: "active",
        orders: 0,
        staff: 1
      };
      setKitchens([...kitchens, newKitchen]);
      setNewKitchenName("");
      setIsAddKitchenOpen(false);
    }
  };

  const handleStatusChange = (orderId: string, newStatus: string) => {
    setOrders(orders.map(order => 
      order.id === orderId ? { ...order, status: newStatus } : order
    ));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending": return "bg-yellow-500 hover:bg-yellow-600";
      case "accepted": return "bg-blue-500 hover:bg-blue-600";
      case "preparing": return "bg-orange-500 hover:bg-orange-600";
      case "cooking": return "bg-purple-500 hover:bg-purple-600";
      case "ready": return "bg-green-500 hover:bg-green-600";
      case "hold": return "bg-red-500 hover:bg-red-600";
      default: return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const getKitchenStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-500";
      case "maintenance": return "bg-red-500";
      case "offline": return "bg-gray-500";
      default: return "bg-gray-500";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "border-l-red-500 bg-red-50";
      case "medium": return "border-l-yellow-500 bg-yellow-50";
      case "low": return "border-l-green-500 bg-green-50";
      default: return "border-l-gray-500 bg-gray-50";
    }
  };

  const filteredOrders = orders.filter(order => {
    if (filterStatus === "all") return true;
    return order.status === filterStatus;
  });

  const kitchenOrders = selectedKitchen 
    ? filteredOrders.filter(order => order.kitchen === selectedKitchen)
    : filteredOrders;

  const OrderCard = ({ order }: { order: any }) => (
    <Card className={`mb-4 border-l-4 ${getPriorityColor(order.priority)} hover:shadow-lg transition-all duration-200`}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg flex items-center gap-2">
              {order.id}
              {order.priority === "high" && <AlertCircle className="w-4 h-4 text-red-500" />}
            </CardTitle>
            <div className="text-sm text-gray-600 mt-1">
              <span className="font-medium">Table {order.tableNumber}</span> • {order.customerName}
            </div>
            <div className="text-xs text-gray-500">Ordered at {order.orderTime}</div>
          </div>
          <Badge className={`${getStatusColor(order.status)} text-white`}>
            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {order.items.map((item: any, index: number) => (
            <div key={index} className="flex items-center gap-3 p-2 bg-white rounded-lg">
              <img 
                src={item.image} 
                alt={item.name}
                className="w-12 h-12 object-cover rounded"
              />
              <div className="flex-1">
                <p className="font-medium">{item.name}</p>
                <p className="text-sm text-gray-600">Qty: {item.quantity} | {item.calories} cal</p>
              </div>
            </div>
          ))}
          
          <div className="flex gap-2 pt-4">
            {order.status === "pending" && (
              <>
                <Button 
                  onClick={() => handleStatusChange(order.id, "accepted")} 
                  size="sm"
                  className="bg-blue-500 hover:bg-blue-600"
                >
                  Accept
                </Button>
                <Button 
                  onClick={() => handleStatusChange(order.id, "hold")} 
                  variant="destructive" 
                  size="sm"
                >
                  Hold
                </Button>
              </>
            )}
            
            {(order.status === "accepted" || order.status === "preparing" || order.status === "cooking") && (
              <select 
                className="px-3 py-1 border rounded-md bg-white"
                value={order.status}
                onChange={(e) => handleStatusChange(order.id, e.target.value)}
              >
                <option value="preparing">Preparing</option>
                <option value="cooking">Cooking</option>
                <option value="ready">Ready for Delivery</option>
                <option value="hold">Hold</option>
              </select>
            )}

            {order.status === "ready" && (
              <Button 
                onClick={() => handleStatusChange(order.id, "completed")} 
                size="sm"
                className="bg-green-600 hover:bg-green-700"
              >
                Mark Delivered
              </Button>
            )}
          </div>
          
          <div className="flex items-center justify-between pt-2 text-sm text-gray-600">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {order.estimatedTime}
              </div>
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4" />
                {order.kitchen}
              </div>
            </div>
            <Badge variant="outline" className={`${order.priority === 'high' ? 'text-red-600 border-red-300' : order.priority === 'medium' ? 'text-yellow-600 border-yellow-300' : 'text-green-600 border-green-300'}`}>
              {order.priority} priority
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const KitchenCard = ({ kitchen }: { kitchen: any }) => (
    <Card className="mb-4 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => setSelectedKitchen(kitchen.name)}>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg">{kitchen.name}</CardTitle>
          <Badge className={getKitchenStatusColor(kitchen.status)}>
            {kitchen.status.charAt(0).toUpperCase() + kitchen.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <Users className="w-4 h-4" />
            {kitchen.orders} active orders
          </div>
          <div className="flex items-center gap-1">
            <Users className="w-4 h-4" />
            {kitchen.staff} staff
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto p-4 lg:p-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold">Kitchen Display System</h1>
            <p className="text-gray-600 mt-1">
              {currentTime.toLocaleDateString()} • {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </p>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {selectedKitchen && (
              <Button
                variant="outline"
                onClick={() => setSelectedKitchen(null)}
                size="sm"
                className="flex items-center gap-2"
              >
                <ChevronLeft className="w-4 h-4" />
                Back to All
              </Button>
            )}
            
            <Button
              variant={isManager ? "default" : "outline"}
              onClick={() => setIsManager(!isManager)}
              size="sm"
              className="flex items-center gap-2"
            >
              <Settings className="w-4 h-4" />
              Manager Mode
            </Button>
            
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              onClick={() => setViewMode("grid")}
              size="sm"
            >
              <Grid className="w-4 h-4" />
            </Button>
            
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              onClick={() => setViewMode("list")}
              size="sm"
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Manager Section */}
        {isManager && !selectedKitchen && (
          <div className="mb-8">
            <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-4 gap-4">
              <h2 className="text-xl lg:text-2xl font-semibold">Kitchen Management</h2>
              <Dialog open={isAddKitchenOpen} onOpenChange={setIsAddKitchenOpen}>
                <DialogTrigger asChild>
                  <Button className="flex items-center gap-2">
                    <Plus className="w-4 h-4" />
                    Add Kitchen
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Kitchen</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="kitchen-name">Kitchen Name</Label>
                      <Input
                        id="kitchen-name"
                        value={newKitchenName}
                        onChange={(e) => setNewKitchenName(e.target.value)}
                        placeholder="Enter kitchen name"
                      />
                    </div>
                    <Button onClick={handleAddKitchen} className="w-full">
                      Add Kitchen
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
            
            <div className={viewMode === "grid" ? "grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-8" : "space-y-4 mb-8"}>
              {kitchens.map((kitchen) => (
                <KitchenCard key={kitchen.id} kitchen={kitchen} />
              ))}
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="flex flex-wrap items-center gap-4 mb-6">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            <span className="text-sm font-medium">Filter by status:</span>
          </div>
          <select 
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-1 border rounded-md bg-white text-sm"
          >
            <option value="all">All Orders</option>
            <option value="pending">Pending</option>
            <option value="preparing">Preparing</option>
            <option value="cooking">Cooking</option>
            <option value="ready">Ready</option>
            <option value="hold">On Hold</option>
          </select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
        </div>

        {/* Orders Section */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl lg:text-2xl font-semibold">
            {selectedKitchen ? `${selectedKitchen} Orders` : 'Active Orders'}
          </h2>
          <div className="text-sm text-gray-600">
            {kitchenOrders.length} orders
          </div>
        </div>
        
        <div className={viewMode === "grid" ? "grid sm:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4" : "space-y-4"}>
          {kitchenOrders.map((order) => (
            <OrderCard key={order.id} order={order} />
          ))}
        </div>

        {kitchenOrders.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No orders found for the selected filters.</p>
          </div>
        )}
      </div>

      {/* Floating Order Now Button */}
      <Link
        to="/menu"
        className="fixed bottom-6 left-6 bg-primary hover:bg-primary/90 text-primary-foreground p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 z-50"
        title="Order Now"
      >
        <ShoppingCart className="w-6 h-6" />
      </Link>
    </div>
  );
};

export default KDS;
