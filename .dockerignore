# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
*.md
docs/

# Tests
tests/
test_*.py
*_test.py

# Logs (keep structure but not content)
logs/*.log
*.log

# Temporary files
*.tmp
*.temp
.cache/

# Frontend
frontend/
node_modules/

# Development files
.env.example
.env.local
.env.development
.env.test

# Database files (will be mounted as volumes)
*.db
*.sqlite
*.sqlite3
