import pandas as pd
import os
from datetime import datetime

# Frontend menu items data (from menuItems.ts)
frontend_menu_items = [
    {
        'id': '1',
        'name': 'Classic Spring Rolls',
        'category': 'appetizer',
        'price': 8.99,
        'calories': 280,
        'description': 'Crispy spring rolls filled with fresh vegetables and served with sweet chili sauce'
    },
    {
        'id': '2',
        'name': 'Buffalo Chicken Wings',
        'category': 'appetizer',
        'price': 12.99,
        'calories': 450,
        'description': 'Spicy buffalo wings served with celery sticks and blue cheese dip'
    },
    {
        'id': '3',
        'name': 'Loaded Nachos',
        'category': 'appetizer',
        'price': 10.99,
        'calories': 620,
        'description': 'Tortilla chips topped with cheese, jalapeños, sour cream, and guacamole'
    },
    {
        'id': '4',
        'name': 'Classic Beef Burger',
        'category': 'hamburger',
        'price': 12.99,
        'calories': 650,
        'description': 'Juicy beef patty with fresh lettuce, tomato, and our special sauce'
    },
    {
        'id': '5',
        'name': 'BBQ Bacon Burger',
        'category': 'hamburger',
        'price': 15.99,
        'calories': 780,
        'description': 'Grilled beef patty with BBQ sauce, crispy bacon, and onion rings'
    },
    {
        'id': '6',
        'name': 'Margherita Pizza',
        'category': 'pizza',
        'price': 14.99,
        'calories': 720,
        'description': 'Traditional pizza with fresh basil, mozzarella, and tomato sauce'
    },
    {
        'id': '7',
        'name': 'Pepperoni Supreme',
        'category': 'pizza',
        'price': 16.99,
        'calories': 850,
        'description': 'Loaded with pepperoni, mushrooms, bell peppers, and extra cheese'
    },
    {
        'id': '8',
        'name': 'Caesar Salad',
        'category': 'salad',
        'price': 9.99,
        'calories': 320,
        'description': 'Crisp romaine lettuce with parmesan cheese and caesar dressing'
    },
    {
        'id': '9',
        'name': 'Greek Salad',
        'category': 'salad',
        'price': 11.99,
        'calories': 280,
        'description': 'Fresh tomatoes, cucumber, olives, and feta cheese with olive oil'
    },
    {
        'id': '10',
        'name': 'Chocolate Cake',
        'category': 'dessert',
        'price': 6.99,
        'calories': 450,
        'description': 'Rich chocolate cake with chocolate frosting'
    },
    {
        'id': '11',
        'name': 'Fresh Orange Juice',
        'category': 'beverage',
        'price': 4.99,
        'calories': 120,
        'description': 'Freshly squeezed orange juice with pulp'
    },
    {
        'id': '12',
        'name': 'Grilled Salmon',
        'category': 'main',
        'price': 22.99,
        'calories': 520,
        'description': 'Atlantic salmon grilled to perfection with lemon butter sauce'
    },
    {
        'id': '13',
        'name': 'Ribeye Steak',
        'category': 'main',
        'price': 28.99,
        'calories': 680,
        'description': '12oz prime ribeye steak cooked to your preference'
    },
    {
        'id': '14',
        'name': 'Chicken Alfredo Pasta',
        'category': 'pasta',
        'price': 16.99,
        'calories': 720,
        'description': 'Creamy alfredo pasta with grilled chicken and parmesan'
    },
    {
        'id': '15',
        'name': 'Fish Tacos',
        'category': 'main',
        'price': 13.99,
        'calories': 480,
        'description': 'Grilled fish tacos with cabbage slaw and lime crema'
    }
]

# Kitchen assignments based on categories
def get_kitchen_for_category(category):
    kitchen_mapping = {
        'appetizer': 'KITCHEN_001',  # Main Kitchen
        'hamburger': 'KITCHEN_001',  # Main Kitchen
        'main': 'KITCHEN_001',       # Main Kitchen
        'pizza': 'KITCHEN_002',      # Pizza Station
        'pasta': 'KITCHEN_002',      # Pizza Station (Italian)
        'salad': 'KITCHEN_003',      # Salad Station
        'dessert': 'KITCHEN_004',    # Dessert Corner
        'beverage': 'KITCHEN_004'    # Dessert Corner (Beverages)
    }
    return kitchen_mapping.get(category, 'KITCHEN_001')

# Prep time based on category
def get_prep_time_for_category(category):
    prep_time_mapping = {
        'appetizer': 8,
        'hamburger': 12,
        'main': 18,
        'pizza': 15,
        'pasta': 14,
        'salad': 6,
        'dessert': 10,
        'beverage': 3
    }
    return prep_time_mapping.get(category, 10)

# Difficulty based on category
def get_difficulty_for_category(category):
    difficulty_mapping = {
        'appetizer': 'easy',
        'hamburger': 'medium',
        'main': 'hard',
        'pizza': 'medium',
        'pasta': 'medium',
        'salad': 'easy',
        'dessert': 'medium',
        'beverage': 'easy'
    }
    return difficulty_mapping.get(category, 'medium')

# Create items dataframe for Excel
items_data = []
for item in frontend_menu_items:
    items_data.append({
        'item_id': str(item['id']),  # Keep the same ID as frontend (as string)
        'item_name': item['name'],
        'kitchen_id': get_kitchen_for_category(item['category']),
        'prep_time_minutes': get_prep_time_for_category(item['category']),
        'difficulty_level': get_difficulty_for_category(item['category']),
        'description': item['description'],
        'price': item['price'],
        'image': f"/menu-item-{item['id']}.jpg",  # Placeholder image path
        'calories': item['calories'],
        'category': item['category'],
        'available': True
    })

# Read existing Excel file to preserve other sheets
excel_file_path = 'data/kitchen_config.xlsx'
new_excel_file_path = 'data/kitchen_config_new.xlsx'

# Read existing sheets
existing_sheets = {}
if os.path.exists(excel_file_path):
    xl_file = pd.ExcelFile(excel_file_path)
    for sheet_name in xl_file.sheet_names:
        if sheet_name != 'items':  # We'll replace the items sheet
            existing_sheets[sheet_name] = pd.read_excel(excel_file_path, sheet_name=sheet_name)

# Create new items DataFrame
items_df = pd.DataFrame(items_data)

# Write to Excel file
with pd.ExcelWriter(new_excel_file_path, engine='openpyxl') as writer:
    # Write the new items sheet
    items_df.to_excel(writer, sheet_name='items', index=False)
    
    # Write back existing sheets
    for sheet_name, df in existing_sheets.items():
        df.to_excel(writer, sheet_name=sheet_name, index=False)

print(f"Created new Excel file with {len(items_data)} menu items from frontend")
print("Items updated:")
for item in items_data:
    print(f"  - {item['item_id']}: {item['item_name']} ({item['category']})")

# Now replace the original file
import shutil
try:
    shutil.move(new_excel_file_path, excel_file_path)
    print(f"Successfully replaced {excel_file_path}")
except Exception as e:
    print(f"Could not replace original file: {e}")
    print(f"New file saved as: {new_excel_file_path}")
