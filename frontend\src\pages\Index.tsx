
import React, { useEffect, useState } from "react";
import Navbar from "@/components/Navbar";
import Hero from "@/components/Hero";
import WhyOurRestaurant from "@/components/WhyOurRestaurant";
import SmartSystem from "@/components/SmartSystem";
import FeaturedItems from "@/components/FeaturedItems";
import SmartRestaurantFeatures from "@/components/SmartRestaurantFeatures";
import SmartTestimonials from "@/components/SmartTestimonials";
import OfferSlider from "@/components/OfferSlider";
import Footer from "@/components/Footer";
import FloatingKDS from "@/components/FloatingKDS";
import Dashboard from "@/components/Dashboard";
import { Button } from "@/components/ui/button";
import { Monitor, Home } from "lucide-react";

const Index = () => {
  const [showDashboard, setShowDashboard] = useState(false);

  // Initialize intersection observer to detect when elements enter viewport
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in");
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = document.querySelectorAll(".animate-on-scroll");
    elements.forEach((el) => observer.observe(el));

    return () => {
      elements.forEach((el) => observer.unobserve(el));
    };
  }, []);

  if (showDashboard) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="pt-16">
          <div className="container mx-auto px-4 py-6">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold">System Dashboard</h1>
              <Button
                variant="outline"
                onClick={() => setShowDashboard(false)}
                className="flex items-center gap-2"
              >
                <Home className="w-4 h-4" />
                Back to Home
              </Button>
            </div>
            <Dashboard />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Navbar />
      <main className="space-y-4 sm:space-y-8">
        <Hero />
        <WhyOurRestaurant />
        <SmartSystem />
        <FeaturedItems />
        <SmartRestaurantFeatures />
        <SmartTestimonials />
        <OfferSlider />
      </main>
      <Footer />
      <FloatingKDS />

      {/* Dashboard Toggle Button */}
      <Button
        onClick={() => setShowDashboard(true)}
        className="fixed bottom-6 left-6 bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 z-50"
        title="System Dashboard"
      >
        <Monitor className="w-6 h-6" />
      </Button>
    </div>
  );
};

export default Index;
