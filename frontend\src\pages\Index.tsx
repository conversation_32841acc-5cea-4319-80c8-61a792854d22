
import React, { useEffect } from "react";
import Navbar from "@/components/Navbar";
import Hero from "@/components/Hero";
import WhyOurRestaurant from "@/components/WhyOurRestaurant";
import SmartSystem from "@/components/SmartSystem";
import FeaturedItems from "@/components/FeaturedItems";
import SmartRestaurantFeatures from "@/components/SmartRestaurantFeatures";
import SmartTestimonials from "@/components/SmartTestimonials";
import OfferSlider from "@/components/OfferSlider";
import Footer from "@/components/Footer";
import FloatingKDS from "@/components/FloatingKDS";

const Index = () => {
  // Initialize intersection observer to detect when elements enter viewport
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in");
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );
    
    const elements = document.querySelectorAll(".animate-on-scroll");
    elements.forEach((el) => observer.observe(el));
    
    return () => {
      elements.forEach((el) => observer.unobserve(el));
    };
  }, []);

  return (
    <div className="min-h-screen">
      <Navbar />
      <main className="space-y-4 sm:space-y-8">
        <Hero />
        <WhyOurRestaurant />
        <SmartSystem />
        <FeaturedItems />
        <SmartRestaurantFeatures />
        <SmartTestimonials />
        <OfferSlider />
      </main>
      <Footer />
      <FloatingKDS />
    </div>
  );
};

export default Index;
