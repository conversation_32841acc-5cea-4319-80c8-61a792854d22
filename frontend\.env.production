# Production Environment Configuration for Frontend
# This file is used when building for production (Docker)

# API Configuration
VITE_API_BASE_URL=http://localhost:8000

# Application Configuration
VITE_APP_NAME=Smart Kitchen Queue Management System
VITE_APP_VERSION=1.0.0

# Feature Flags
VITE_ENABLE_DEBUG=false
VITE_ENABLE_MOCK_DATA=false

# Performance
VITE_API_TIMEOUT=10000
VITE_API_RETRIES=3

# Logging
VITE_LOG_LEVEL=warn
