"""
MongoDB service for the Smart Kitchen Queue Management System.
"""

import logging
import os
from typing import Dict, List, Optional, Any
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import DuplicateKeyError, ConnectionFailure
import asyncio

logger = logging.getLogger(__name__)


class MongoDBService:
    """MongoDB service for data persistence."""
    
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.db: Optional[AsyncIOMotorDatabase] = None
        self._connection_string = os.getenv(
            "MONGODB_URL", 
            "mongodb://127.0.0.1:27017"
        )
        self._database_name = os.getenv("MONGODB_DATABASE", "smart_kitchen")
        
    async def connect(self):
        """Connect to MongoDB."""
        try:
            self.client = AsyncIOMotorClient(self._connection_string)
            self.db = self.client[self._database_name]
            
            # Test connection
            await self.client.admin.command('ping')
            logger.info(f"Connected to MongoDB: {self._database_name}")
            
            # Create indexes
            await self._create_indexes()
            
        except ConnectionFailure as e:
            logger.warning(f"Failed to connect to MongoDB: {e}")
            logger.info("Falling back to in-memory storage")
            # Fallback to in-memory storage
            self.client = None
            self.db = None
            self._init_fallback_storage()
        except Exception as e:
            logger.warning(f"MongoDB connection error: {e}")
            logger.info("Falling back to in-memory storage")
            self.client = None
            self.db = None
            self._init_fallback_storage()

    def _init_fallback_storage(self):
        """Initialize in-memory fallback storage."""
        self._fallback_storage = {
            "menu_items": {},
            "kitchens": {},
            "orders": {},
            "queue_items": {}
        }
            
    async def disconnect(self):
        """Disconnect from MongoDB."""
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
            
    async def _create_indexes(self):
        """Create database indexes."""
        if not self.db:
            return
            
        try:
            # Menu items indexes
            await self.db.menu_items.create_index("item_id", unique=True)
            await self.db.menu_items.create_index("category")
            await self.db.menu_items.create_index("available")
            
            # Kitchen indexes
            await self.db.kitchens.create_index("kitchen_id", unique=True)
            await self.db.kitchens.create_index("status")
            
            # Orders indexes
            await self.db.orders.create_index("order_id", unique=True)
            await self.db.orders.create_index("status")
            await self.db.orders.create_index("timestamp")
            
            # Queue items indexes
            await self.db.queue_items.create_index("queue_id", unique=True)
            await self.db.queue_items.create_index("order_id")
            await self.db.queue_items.create_index("kitchen_id")
            await self.db.queue_items.create_index("status")
            
            logger.info("MongoDB indexes created successfully")
            
        except Exception as e:
            logger.error(f"Error creating indexes: {e}")
    
    # Menu Items Operations
    async def get_menu_items(self, category: Optional[str] = None, available_only: bool = True) -> List[Dict]:
        """Get menu items."""
        if not self.db:
            # Fallback to in-memory storage
            items = list(self._fallback_storage["menu_items"].values())

            # Apply filters
            if available_only:
                items = [item for item in items if item.get("available", True)]
            if category:
                items = [item for item in items if item.get("category") == category]

            return items

        try:
            filter_query = {}
            if category:
                filter_query["category"] = category
            if available_only:
                filter_query["available"] = True

            cursor = self.db.menu_items.find(filter_query)
            items = await cursor.to_list(length=None)

            # Convert ObjectId to string
            for item in items:
                item["_id"] = str(item["_id"])

            return items

        except Exception as e:
            logger.error(f"Error getting menu items: {e}")
            return []
    
    async def get_menu_item(self, item_id: str) -> Optional[Dict]:
        """Get a specific menu item."""
        if not self.db:
            # Fallback to in-memory storage
            return self._fallback_storage["menu_items"].get(item_id)

        try:
            item = await self.db.menu_items.find_one({"item_id": item_id})
            if item:
                item["_id"] = str(item["_id"])
            return item

        except Exception as e:
            logger.error(f"Error getting menu item {item_id}: {e}")
            return None
    
    async def upsert_menu_items(self, items: List[Dict]) -> bool:
        """Insert or update menu items."""
        if not self.db:
            # Fallback to in-memory storage
            for item in items:
                item["updated_at"] = datetime.now()
                self._fallback_storage["menu_items"][item["item_id"]] = item
            logger.info(f"Upserted {len(items)} menu items to fallback storage")
            return True

        try:
            operations = []
            for item in items:
                # Add timestamp
                item["updated_at"] = datetime.now()

                operations.append({
                    "replaceOne": {
                        "filter": {"item_id": item["item_id"]},
                        "replacement": item,
                        "upsert": True
                    }
                })

            if operations:
                result = await self.db.menu_items.bulk_write(operations)
                logger.info(f"Upserted {result.upserted_count + result.modified_count} menu items")

            return True

        except Exception as e:
            logger.error(f"Error upserting menu items: {e}")
            return False
    
    # Kitchen Operations
    async def get_kitchens(self) -> List[Dict]:
        """Get all kitchens."""
        if not self.db:
            return []
            
        try:
            cursor = self.db.kitchens.find({})
            kitchens = await cursor.to_list(length=None)
            
            for kitchen in kitchens:
                kitchen["_id"] = str(kitchen["_id"])
                
            return kitchens
            
        except Exception as e:
            logger.error(f"Error getting kitchens: {e}")
            return []
    
    async def upsert_kitchens(self, kitchens: List[Dict]) -> bool:
        """Insert or update kitchens."""
        if not self.db:
            return False
            
        try:
            operations = []
            for kitchen in kitchens:
                kitchen["updated_at"] = datetime.now()
                
                operations.append({
                    "replaceOne": {
                        "filter": {"kitchen_id": kitchen["kitchen_id"]},
                        "replacement": kitchen,
                        "upsert": True
                    }
                })
            
            if operations:
                result = await self.db.kitchens.bulk_write(operations)
                logger.info(f"Upserted {result.upserted_count + result.modified_count} kitchens")
                
            return True
            
        except Exception as e:
            logger.error(f"Error upserting kitchens: {e}")
            return False
    
    # Order Operations
    async def create_order(self, order_data: Dict) -> Optional[str]:
        """Create a new order."""
        if not self.db:
            return None
            
        try:
            order_data["created_at"] = datetime.now()
            order_data["updated_at"] = datetime.now()
            
            result = await self.db.orders.insert_one(order_data)
            logger.info(f"Created order: {order_data['order_id']}")
            
            return order_data["order_id"]
            
        except DuplicateKeyError:
            logger.error(f"Order {order_data['order_id']} already exists")
            return None
        except Exception as e:
            logger.error(f"Error creating order: {e}")
            return None
    
    async def get_orders(self, status: Optional[str] = None, limit: int = 100) -> List[Dict]:
        """Get orders with optional status filter."""
        if not self.db:
            return []
            
        try:
            filter_query = {}
            if status:
                filter_query["status"] = status
                
            cursor = self.db.orders.find(filter_query).sort("timestamp", -1).limit(limit)
            orders = await cursor.to_list(length=None)
            
            for order in orders:
                order["_id"] = str(order["_id"])
                
            return orders
            
        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return []
    
    async def update_order_status(self, order_id: str, status: str) -> bool:
        """Update order status."""
        if not self.db:
            return False
            
        try:
            result = await self.db.orders.update_one(
                {"order_id": order_id},
                {
                    "$set": {
                        "status": status,
                        "updated_at": datetime.now()
                    }
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating order status: {e}")
            return False
    
    # Queue Operations
    async def get_queue_items(self, kitchen_id: Optional[str] = None, status: Optional[str] = None) -> List[Dict]:
        """Get queue items."""
        if not self.db:
            return []
            
        try:
            filter_query = {}
            if kitchen_id:
                filter_query["kitchen_id"] = kitchen_id
            if status:
                filter_query["status"] = status
                
            cursor = self.db.queue_items.find(filter_query).sort("scheduled_start", 1)
            items = await cursor.to_list(length=None)
            
            for item in items:
                item["_id"] = str(item["_id"])
                
            return items
            
        except Exception as e:
            logger.error(f"Error getting queue items: {e}")
            return []
    
    async def upsert_queue_item(self, queue_item: Dict) -> bool:
        """Insert or update a queue item."""
        if not self.db:
            return False
            
        try:
            queue_item["updated_at"] = datetime.now()
            
            result = await self.db.queue_items.replace_one(
                {"queue_id": queue_item["queue_id"]},
                queue_item,
                upsert=True
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error upserting queue item: {e}")
            return False
    
    # Health check
    async def health_check(self) -> Dict[str, Any]:
        """Check MongoDB health."""
        if not self.client:
            return {
                "status": "disconnected",
                "message": "MongoDB client not initialized"
            }
            
        try:
            await self.client.admin.command('ping')
            
            # Get collection stats
            stats = {}
            collections = ["menu_items", "kitchens", "orders", "queue_items"]
            
            for collection_name in collections:
                count = await self.db[collection_name].count_documents({})
                stats[collection_name] = count
            
            return {
                "status": "connected",
                "database": self._database_name,
                "collections": stats
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }


# Global MongoDB service instance
mongodb_service = MongoDBService()
